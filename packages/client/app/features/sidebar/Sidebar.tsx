import { useLocation, useNavigate } from "react-router";
import { TabList, Tab, makeStyles, tokens } from "@fluentui/react-components";

const useStyles = makeStyles({
  sidebar: {
    width: "240px",
    height: "100%",
    backgroundColor: tokens.colorNeutralBackground3,
    borderRight: `2px solid ${tokens.colorBrandBackground}`,
    display: "flex",
    flexDirection: "column",
    flexShrink: 0,
  },
  navigation: {
    padding: tokens.spacingVerticalM,
    flex: 1,
  },
  tabList: {
    flexDirection: "column",
    alignItems: "stretch",
  },
  tab: {
    justifyContent: "flex-start",
    padding: `${tokens.spacingVerticalS} ${tokens.spacingHorizontalM}`,
    marginBottom: tokens.spacingVerticalXS,
    borderRadius: tokens.borderRadiusMedium,
    minHeight: "40px",
    width: "100%",
  },
});

interface NavigationItem {
  id: string;
  label: string;
  path: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: "requirements",
    label: "Requirements",
    path: "/requirements",
  },
  {
    id: "initiatives",
    label: "Initiatives",
    path: "/initiatives",
  },
  {
    id: "personas",
    label: "Personas",
    path: "/personas",
  },
];

export function Sidebar() {
  const styles = useStyles();
  const location = useLocation();
  const navigate = useNavigate();

  // Determine the active tab based on current path
  const getActiveTab = () => {
    const currentPath = location.pathname;
    const activeItem = navigationItems.find((item) =>
      currentPath.startsWith(item.path)
    );
    return activeItem?.id || "requirements"; // Default to requirements
  };

  const handleTabSelect = (_event: any, data: any) => {
    const selectedItem = navigationItems.find((item) => item.id === data.value);
    if (selectedItem) {
      navigate(selectedItem.path);
    }
  };

  console.log("Sidebar rendered");

  return (
    <aside className={styles.sidebar}>
      <nav className={styles.navigation}>
        <TabList
          selectedValue={getActiveTab()}
          onTabSelect={handleTabSelect}
          vertical
          className={styles.tabList}
        >
          {navigationItems.map((item) => (
            <Tab key={item.id} value={item.id} className={styles.tab}>
              {item.label}
            </Tab>
          ))}
        </TabList>
      </nav>
    </aside>
  );
}
