import { useNavigate } from "react-router";
import {
  <PERSON>,
  Avatar,
  <PERSON>u,
  <PERSON>u<PERSON><PERSON><PERSON>,
  MenuP<PERSON>over,
  MenuList,
  MenuItem,
  makeStyles,
  tokens,
} from "@fluentui/react-components";
import { useAuth } from "../auth/AuthContext";

const useStyles = makeStyles({
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    padding: `${tokens.spacingVerticalM} ${tokens.spacingHorizontalL}`,
    backgroundColor: tokens.colorNeutralBackground1,
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    height: "60px",
    boxSizing: "border-box",
  },
  logo: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalS,
  },
  userSection: {
    display: "flex",
    alignItems: "center",
    gap: tokens.spacingHorizontalM,
  },
});

export function Header() {
  const styles = useStyles();
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
    navigate("/login", { replace: true });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <header className={styles.header}>
      <div className={styles.logo}>
        <Text size={500} weight="semibold">
          Vast
        </Text>
      </div>

      <div className={styles.userSection}>
        <Menu>
          <MenuTrigger disableButtonEnhancement>
            <Avatar
              style={{ cursor: "pointer" }}
              name={user?.name}
              initials={user?.name ? getInitials(user.name) : "U"}
              size={32}
            />
          </MenuTrigger>

          <MenuPopover>
            <MenuList>
              <MenuItem onClick={handleLogout}>Sign Out</MenuItem>
            </MenuList>
          </MenuPopover>
        </Menu>
      </div>
    </header>
  );
}
