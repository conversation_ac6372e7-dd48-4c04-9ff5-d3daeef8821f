import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router";
import type { Route } from "./+types/initiatives.$id";
import {
  Text,
  Card,
  CardHeader,
  Button,
  Tag,
  makeStyles,
  tokens,
  Title1,
  Body1,
  Body2,
  Subtitle1,
} from "@fluentui/react-components";
import { ArrowLeft24Regular } from "@fluentui/react-icons";

// Types (shared with initiatives page)
interface Initiative {
  id: string;
  initiativeId: string;
  name: string;
  status: "Backlog" | "To do" | "In Progress" | "Done";
  priority: "Low" | "Medium" | "High" | "Not set";
  description?: string;
  owner?: string;
  startDate?: string;
  endDate?: string;
}

// Mock data (in a real app, this would come from an API)
const mockInitiatives: Initiative[] = [
  {
    id: "1",
    initiativeId: "INT-001",
    name: "Customer Experience Enhancement",
    status: "In Progress",
    priority: "High",
    description: "Improve overall customer experience across all touchpoints including website, mobile app, and customer service interactions. This initiative aims to increase customer satisfaction scores by 25% and reduce customer churn by 15%.",
    owner: "<PERSON>",
    startDate: "2024-01-15",
    endDate: "2024-06-30",
  },
  {
    id: "2",
    initiativeId: "INT-002",
    name: "Digital Transformation Initiative",
    status: "To do",
    priority: "Medium",
    description: "Modernize legacy systems and implement new digital tools to improve operational efficiency and enable better data-driven decision making.",
    owner: "Michael Chen",
    startDate: "2024-03-01",
    endDate: "2024-12-31",
  },
  {
    id: "3",
    initiativeId: "INT-003",
    name: "Market Expansion Strategy",
    status: "Backlog",
    priority: "Low",
    description: "Explore and enter new geographic markets to expand our customer base and increase revenue streams.",
    owner: "Emily Rodriguez",
    startDate: "2024-07-01",
    endDate: "2025-03-31",
  },
  {
    id: "4",
    initiativeId: "INT-004",
    name: "Product Innovation Program",
    status: "Done",
    priority: "High",
    description: "Develop next-generation products that meet evolving customer needs and maintain competitive advantage in the market.",
    owner: "David Kim",
    startDate: "2023-09-01",
    endDate: "2024-02-29",
  },
];

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalL,
    maxWidth: "1200px",
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  backButton: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: "100%",
  },
  cardContent: {
    padding: tokens.spacingHorizontalL,
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalM,
  },
  detailRow: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalXS,
  },
  detailGrid: {
    display: "grid",
    gridTemplateColumns: "1fr 1fr",
    gap: tokens.spacingHorizontalL,
    "@media (max-width: 768px)": {
      gridTemplateColumns: "1fr",
    },
  },
  notFound: {
    textAlign: "center",
    padding: tokens.spacingVerticalXXL,
  },
});

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `Initiative ${params.id} - Vast` },
    { name: "description", content: "View initiative details" },
  ];
}

export default function InitiativeDetail() {
  const styles = useStyles();
  const { id } = useParams();
  const navigate = useNavigate();
  const [initiative, setInitiative] = useState<Initiative | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call to fetch initiative by ID
    const fetchInitiative = () => {
      setLoading(true);
      // In a real app, this would be an API call
      const found = mockInitiatives.find(init => init.id === id);
      setInitiative(found || null);
      setLoading(false);
    };

    fetchInitiative();
  }, [id]);

  const getStatusTag = (status: Initiative["status"]) => {
    return <Tag size="small">{status}</Tag>;
  };

  const getPriorityTag = (priority: Initiative["priority"]) => {
    return <Tag size="small">{priority}</Tag>;
  };

  const handleBack = () => {
    navigate("/initiatives");
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <Text size={400}>Loading...</Text>
      </div>
    );
  }

  if (!initiative) {
    return (
      <div className={styles.container}>
        <Button
          appearance="subtle"
          icon={<ArrowLeft24Regular />}
          onClick={handleBack}
          className={styles.backButton}
        >
          Back to Initiatives
        </Button>
        <Card className={styles.notFound}>
          <CardHeader>
            <Title1>Initiative Not Found</Title1>
          </CardHeader>
          <div className={styles.cardContent}>
            <Body1>The initiative with ID "{id}" could not be found.</Body1>
            <Button appearance="primary" onClick={handleBack}>
              Return to Initiatives
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <Button
        appearance="subtle"
        icon={<ArrowLeft24Regular />}
        onClick={handleBack}
        className={styles.backButton}
      >
        Back to Initiatives
      </Button>

      <div className={styles.header}>
        <Title1 block>{initiative.name}</Title1>
        <Body2 block>{initiative.initiativeId}</Body2>
      </div>

      <Card className={styles.card}>
        <CardHeader>
          <Subtitle1>Initiative Details</Subtitle1>
        </CardHeader>
        <div className={styles.cardContent}>
          <div className={styles.detailRow}>
            <Text weight="semibold">Description</Text>
            <Body1>{initiative.description || "No description provided"}</Body1>
          </div>

          <div className={styles.detailGrid}>
            <div className={styles.detailRow}>
              <Text weight="semibold">Status</Text>
              {getStatusTag(initiative.status)}
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">Priority</Text>
              {getPriorityTag(initiative.priority)}
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">Owner</Text>
              <Body1>{initiative.owner || "Not assigned"}</Body1>
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">Initiative ID</Text>
              <Body1>{initiative.initiativeId}</Body1>
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">Start Date</Text>
              <Body1>{formatDate(initiative.startDate)}</Body1>
            </div>
            <div className={styles.detailRow}>
              <Text weight="semibold">End Date</Text>
              <Body1>{formatDate(initiative.endDate)}</Body1>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
