import type { Route } from "./+types/initiatives";
import {
  Text,
  Card,
  CardHeader,
  CardPreview,
  makeStyles,
  tokens,
} from "@fluentui/react-components";

// Types
interface Initiative {
  id: string;
  initiativeId: string;
  name: string;
  status: "Backlog" | "To do" | "In Progress" | "Done";
  priority: "Low" | "Medium" | "High" | "Not set";
}

// Mock data
const mockInitiatives: Initiative[] = [
  {
    id: "1",
    initiativeId: "INT-001",
    name: "Customer Experience Enhancement",
    status: "In Progress",
    priority: "High",
  },
  {
    id: "2",
    initiativeId: "INT-002",
    name: "Digital Transformation Initiative",
    status: "To do",
    priority: "Medium",
  },
  {
    id: "3",
    initiativeId: "INT-003",
    name: "Market Expansion Strategy",
    status: "Backlog",
    priority: "Low",
  },
  {
    id: "4",
    initiativeId: "INT-004",
    name: "Product Innovation Program",
    status: "Done",
    priority: "High",
  },
];

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalL,
    maxWidth: "1200px",
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: "100%",
  },
  cardContent: {
    padding: tokens.spacingHorizontalL,
  },
  tableContainer: {
    overflowX: "auto",
  },
  table: {
    width: "100%",
    borderCollapse: "collapse",
    fontSize: tokens.fontSizeBase300,
  },
  tableHeader: {
    backgroundColor: tokens.colorNeutralBackground2,
    borderBottom: `2px solid ${tokens.colorNeutralStroke2}`,
  },
  tableHeaderCell: {
    padding: tokens.spacingHorizontalM,
    textAlign: "left",
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  tableRow: {
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    "&:hover": {
      backgroundColor: tokens.colorNeutralBackground1Hover,
    },
  },
  tableCell: {
    padding: tokens.spacingHorizontalM,
    color: tokens.colorNeutralForeground1,
  },
  emptyStateCard: {
    textAlign: "center",
    padding: tokens.spacingVerticalXXL,
  },
  createButton: {
    marginTop: tokens.spacingVerticalL,
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Initiatives - Vast" },
    {
      name: "description",
      content: "Track and manage your strategic initiatives",
    },
  ];
}

export default function Initiatives() {
  const styles = useStyles();

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Text size={800} weight="semibold">
          Initiatives
        </Text>
        <Text size={400} style={{ marginTop: tokens.spacingVerticalXS }}>
          Track and manage your strategic initiatives
        </Text>
      </div>

      <Card className={styles.card}>
        <CardHeader>
          <Text size={500} weight="semibold">
            Strategic Overview
          </Text>
        </CardHeader>
        <CardPreview>
          <div className={styles.cardContent}>
            <Text size={300}>
              Initiatives help you organize and track high-level strategic
              goals. Use this section to define key objectives, assign
              resources, and monitor progress toward your organization's most
              important outcomes.
            </Text>
          </div>
        </CardPreview>
      </Card>

      <Card className={styles.card}>
        <CardHeader>
          <Text size={500} weight="semibold">
            Active Initiatives
          </Text>
        </CardHeader>
        <CardPreview>
          <div className={styles.cardContent}>
            <Text size={300}>
              No initiatives have been created yet. Start by defining your first
              strategic initiative to begin tracking progress toward your key
              objectives.
            </Text>
          </div>
        </CardPreview>
      </Card>
    </div>
  );
}
