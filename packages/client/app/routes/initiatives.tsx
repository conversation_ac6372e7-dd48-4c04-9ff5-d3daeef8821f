import { useState } from "react";
import type { Route } from "./+types/initiatives";
import {
  Text,
  Card,
  CardHeader,
  CardPreview,
  Button,
  Badge,
  makeStyles,
  tokens,
} from "@fluentui/react-components";
import { Add24Regular } from "@fluentui/react-icons";

// Types
interface Initiative {
  id: string;
  initiativeId: string;
  name: string;
  status: "Backlog" | "To do" | "In Progress" | "Done";
  priority: "Low" | "Medium" | "High" | "Not set";
}

// Mock data
const mockInitiatives: Initiative[] = [
  {
    id: "1",
    initiativeId: "INT-001",
    name: "Customer Experience Enhancement",
    status: "In Progress",
    priority: "High",
  },
  {
    id: "2",
    initiativeId: "INT-002",
    name: "Digital Transformation Initiative",
    status: "To do",
    priority: "Medium",
  },
  {
    id: "3",
    initiativeId: "INT-003",
    name: "Market Expansion Strategy",
    status: "Backlog",
    priority: "Low",
  },
  {
    id: "4",
    initiativeId: "INT-004",
    name: "Product Innovation Program",
    status: "Done",
    priority: "High",
  },
];

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalL,
    maxWidth: "1200px",
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: "100%",
  },
  cardContent: {
    padding: tokens.spacingHorizontalL,
  },
  tableContainer: {
    overflowX: "auto",
    padding: tokens.spacingHorizontalL,
  },
  table: {
    width: "100%",
    borderCollapse: "collapse",
    fontSize: tokens.fontSizeBase300,
  },
  tableHeader: {
    backgroundColor: tokens.colorNeutralBackground2,
    borderBottom: `2px solid ${tokens.colorNeutralStroke2}`,
  },
  tableHeaderCell: {
    padding: tokens.spacingHorizontalM,
    textAlign: "left",
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  tableRow: {
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    "&:hover": {
      backgroundColor: tokens.colorNeutralBackground1Hover,
    },
  },
  tableCell: {
    padding: tokens.spacingHorizontalM,
    color: tokens.colorNeutralForeground1,
  },
  emptyStateCard: {
    textAlign: "center",
  },
  createButton: {
    marginTop: tokens.spacingVerticalL,
  },
  headerActions: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Initiatives - Vast" },
    {
      name: "description",
      content: "Track and manage your strategic initiatives",
    },
  ];
}

export default function Initiatives() {
  const styles = useStyles();
  const [initiatives] = useState<Initiative[]>(mockInitiatives);

  const getStatusBadge = (status: Initiative["status"]) => {
    const statusConfig = {
      Backlog: { appearance: "outline" as const, color: "important" as const },
      "To do": {
        appearance: "outline" as const,
        color: "informative" as const,
      },
      "In Progress": { appearance: "filled" as const, color: "brand" as const },
      Done: { appearance: "filled" as const, color: "success" as const },
    };

    const config = statusConfig[status];
    return (
      <Badge appearance={config.appearance} color={config.color}>
        {status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: Initiative["priority"]) => {
    const priorityConfig = {
      High: { appearance: "filled" as const, color: "danger" as const },
      Medium: { appearance: "filled" as const, color: "warning" as const },
      Low: { appearance: "outline" as const, color: "subtle" as const },
      "Not set": { appearance: "outline" as const, color: "subtle" as const },
    };

    const config = priorityConfig[priority];
    return (
      <Badge appearance={config.appearance} color={config.color}>
        {priority}
      </Badge>
    );
  };

  const handleCreateInitiative = () => {
    // TODO: Implement create initiative functionality
    console.log("Create initiative clicked");
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Text size={800} weight="semibold">
          Initiatives
        </Text>
        <Text size={400} style={{ marginTop: tokens.spacingVerticalXS }}>
          Track and manage your strategic initiatives
        </Text>
      </div>

      {initiatives.length === 0 ? (
        // Empty state
        <Card className={styles.emptyStateCard}>
          <CardHeader>
            <Text size={500} weight="semibold">
              No Initiatives Yet
            </Text>
          </CardHeader>
          <CardPreview>
            <div className={styles.cardContent}>
              <Text size={300}>
                Start by creating your first strategic initiative to begin
                tracking progress toward your key objectives.
              </Text>
              <Button
                appearance="primary"
                icon={<Add24Regular />}
                className={styles.createButton}
                onClick={handleCreateInitiative}
              >
                Create Initiative
              </Button>
            </div>
          </CardPreview>
        </Card>
      ) : (
        // Table with initiatives
        <Card className={styles.card}>
          <CardHeader>
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                width: "100%",
              }}
            >
              <Text size={500} weight="semibold">
                Active Initiatives
              </Text>
              <Button
                appearance="primary"
                icon={<Add24Regular />}
                onClick={handleCreateInitiative}
              >
                Create Initiative
              </Button>
            </div>
          </CardHeader>
          <CardPreview>
            <div className={styles.tableContainer}>
              <table className={styles.table}>
                <thead className={styles.tableHeader}>
                  <tr>
                    <th className={styles.tableHeaderCell}>Initiative ID</th>
                    <th className={styles.tableHeaderCell}>Initiative Name</th>
                    <th className={styles.tableHeaderCell}>Status</th>
                    <th className={styles.tableHeaderCell}>Priority</th>
                  </tr>
                </thead>
                <tbody>
                  {initiatives.map((initiative) => (
                    <tr key={initiative.id} className={styles.tableRow}>
                      <td className={styles.tableCell}>
                        <Text weight="semibold">{initiative.initiativeId}</Text>
                      </td>
                      <td className={styles.tableCell}>
                        <Text>{initiative.name}</Text>
                      </td>
                      <td className={styles.tableCell}>
                        {getStatusBadge(initiative.status)}
                      </td>
                      <td className={styles.tableCell}>
                        {getPriorityBadge(initiative.priority)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardPreview>
        </Card>
      )}
    </div>
  );
}
