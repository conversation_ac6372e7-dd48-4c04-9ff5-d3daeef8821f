import { useState } from "react";
import type { Route } from "./+types/initiatives";
import {
  Text,
  Card,
  CardHeader,
  Button,
  Tag,
  makeStyles,
  tokens,
  Table,
  TableBody,
  TableCell,
  TableRow,
  TableHeader,
  TableHeaderCell,
  TableCellLayout,
  Title1,
  Body2,
} from "@fluentui/react-components";

// Types
interface Initiative {
  id: string;
  initiativeId: string;
  name: string;
  status: "Backlog" | "To do" | "In Progress" | "Done";
  priority: "Low" | "Medium" | "High" | "Not set";
}

// Mock data
const mockInitiatives: Initiative[] = [
  {
    id: "1",
    initiativeId: "INT-001",
    name: "Customer Experience Enhancement",
    status: "In Progress",
    priority: "High",
  },
  {
    id: "2",
    initiativeId: "INT-002",
    name: "Digital Transformation Initiative",
    status: "To do",
    priority: "Medium",
  },
  {
    id: "3",
    initiativeId: "INT-003",
    name: "Market Expansion Strategy",
    status: "Backlog",
    priority: "Low",
  },
  {
    id: "4",
    initiativeId: "INT-004",
    name: "Product Innovation Program",
    status: "Done",
    priority: "High",
  },
];

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalL,
    maxWidth: "1200px",
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: "100%",
  },
  cardContent: {
    padding: tokens.spacingHorizontalL,
  },
  tableContainer: {
    padding: tokens.spacingHorizontalL,
  },
  emptyStateCard: {
    textAlign: "center",
  },
  createButton: {
    marginTop: tokens.spacingVerticalL,
  },
  headerActions: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  // Status tag styles
  statusBacklog: {
    backgroundColor: tokens.colorPaletteRedBackground1,
    color: tokens.colorPaletteRedForeground1,
  },
  statusTodo: {
    backgroundColor: tokens.colorPaletteBlueBackground2,
    color: tokens.colorPaletteBlueForeground2,
  },
  statusInProgress: {
    backgroundColor: tokens.colorBrandBackground,
    color: tokens.colorNeutralForegroundOnBrand,
  },
  statusDone: {
    backgroundColor: tokens.colorPaletteGreenBackground1,
    color: tokens.colorPaletteGreenForeground1,
  },
  // Priority tag styles
  priorityHigh: {
    backgroundColor: tokens.colorPaletteRedBackground1,
    color: tokens.colorPaletteRedForeground1,
  },
  priorityMedium: {
    backgroundColor: tokens.colorPaletteYellowBackground1,
    color: tokens.colorPaletteYellowForeground1,
  },
  priorityLow: {
    backgroundColor: tokens.colorPaletteBlueBackground2,
    color: tokens.colorPaletteBlueForeground2,
  },
  priorityNotSet: {
    backgroundColor: tokens.colorNeutralBackground3,
    color: tokens.colorNeutralForeground2,
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Initiatives - Vast" },
    {
      name: "description",
      content: "Track and manage your strategic initiatives",
    },
  ];
}

export default function Initiatives() {
  const styles = useStyles();
  const [initiatives] = useState<Initiative[]>(mockInitiatives); // Change to [] to test empty state

  const getStatusBadge = (status: Initiative["status"]) => {
    const statusStyleMap = {
      Backlog: styles.statusBacklog,
      "To do": styles.statusTodo,
      "In Progress": styles.statusInProgress,
      Done: styles.statusDone,
    };

    return (
      <Tag size="small" className={statusStyleMap[status]}>
        {status}
      </Tag>
    );
  };

  const getPriorityBadge = (priority: Initiative["priority"]) => {
    const priorityConfig = {
      High: { appearance: "filled" as const, color: "danger" as const },
      Medium: { appearance: "filled" as const, color: "warning" as const },
      Low: { appearance: "filled" as const, color: "informative" as const },
      "Not set": { appearance: "outline" as const, color: "subtle" as const },
    };

    const config = priorityConfig[priority];
    return (
      <Tag appearance={config.appearance} color={config.color} size="small">
        {priority}
      </Tag>
    );
  };

  const handleCreateInitiative = () => {
    // TODO: Implement create initiative functionality
    console.log("Create initiative clicked");
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        {initiatives.length > 0 ? (
          <div className={styles.headerActions}>
            <div>
              <Title1 block>Initiatives</Title1>
              <Body2 block>Track and manage your strategic initiatives</Body2>
            </div>
            <Button appearance="primary" onClick={handleCreateInitiative}>
              Create Initiative
            </Button>
          </div>
        ) : (
          <div>
            <Title1 block>Initiatives</Title1>
            <Body2 block>Track and manage your strategic initiatives</Body2>
          </div>
        )}
      </div>

      {initiatives.length === 0 ? (
        // Empty state
        <Card className={styles.emptyStateCard}>
          <CardHeader>
            <Text size={500} weight="semibold">
              No Initiatives Yet
            </Text>
          </CardHeader>
          <div className={styles.cardContent}>
            <Text size={300}>
              Start by creating your first strategic initiative to begin
              tracking progress toward your key objectives.
            </Text>
            <Button
              appearance="primary"
              className={styles.createButton}
              onClick={handleCreateInitiative}
            >
              Create Initiative
            </Button>
          </div>
        </Card>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHeaderCell>Initiative ID</TableHeaderCell>
              <TableHeaderCell>Initiative Name</TableHeaderCell>
              <TableHeaderCell>Status</TableHeaderCell>
              <TableHeaderCell>Priority</TableHeaderCell>
            </TableRow>
          </TableHeader>
          <TableBody>
            {initiatives.map((initiative) => (
              <TableRow key={initiative.id}>
                <TableCell>
                  <TableCellLayout>
                    <Text weight="semibold">{initiative.initiativeId}</Text>
                  </TableCellLayout>
                </TableCell>
                <TableCell>
                  <TableCellLayout>
                    <Text>{initiative.name}</Text>
                  </TableCellLayout>
                </TableCell>
                <TableCell>
                  <TableCellLayout>
                    {getStatusBadge(initiative.status)}
                  </TableCellLayout>
                </TableCell>
                <TableCell>
                  <TableCellLayout>
                    {getPriorityBadge(initiative.priority)}
                  </TableCellLayout>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
