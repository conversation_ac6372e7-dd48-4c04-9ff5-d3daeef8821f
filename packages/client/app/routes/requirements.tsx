import type { Route } from "./+types/requirements";
import {
  Text,
  Card,
  CardHeader,
  makeStyles,
  tokens,
  Title1,
  Subtitle1,
} from "@fluentui/react-components";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: tokens.spacingVerticalL,
    maxWidth: "1200px",
    alignItems: "baseline",
  },
  header: {
    marginBottom: tokens.spacingVerticalM,
  },
  card: {
    width: "360px",
  },
});

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Requirements - Vast" },
    { name: "description", content: "Manage your project requirements" },
  ];
}

console.log("Requirements rendered");

export default function Requirements() {
  const styles = useStyles();

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Title1 block>Requirements</Title1>
        <Subtitle1 block>Define and manage your project requirements</Subtitle1>
      </div>

      <Card className={styles.card}>
        <CardHeader
          header={<Text weight="semibold">Getting Started</Text>}
        ></CardHeader>
        <Text size={300}>
          This is where you'll manage your project requirements. You can create,
          edit, and organize requirements to ensure your project meets all
          necessary specifications and stakeholder needs.
        </Text>
      </Card>

      <Card className={styles.card}>
        <CardHeader
          header={<Text weight="semibold">Recent Requirements</Text>}
        ></CardHeader>
        <Text size={300}>
          No requirements have been created yet. Start by adding your first
          requirement to begin organizing your project specifications.
        </Text>
      </Card>
    </div>
  );
}
